# OmniParser MCP Server

一個基於Microsoft OmniParser模型的MCP (Model Context Protocol) Server，用於UI自動化和屏幕解析。

## 功能特性

- 🖥️ **屏幕截圖解析**: 使用OmniParser模型解析UI截圖，識別可交互元素
- 🖱️ **鍵鼠模擬**: 支持鍵盤和滑鼠操作模擬
- 🪟 **視窗管理**: 連接和管理特定應用程式視窗
- 🌐 **多平台支持**: 支持桌面應用、瀏覽器和遊戲
- 🔧 **MCP協議**: 完全兼容MCP協議，可與支持MCP的AI助手集成

## 安裝

### 前置要求

- Python 3.10+
- CUDA支持的GPU (推薦)
- OmniParser模型權重文件

### 安裝步驟

1. 克隆項目:
```bash
git clone <repository-url>
cd omniparser-mcp
```

2. 安裝依賴:
```bash
pip install -e .
```

3. 下載OmniParser模型權重:
```bash
# 下載模型權重到weights目錄
mkdir -p weights
# 按照OmniParser官方說明下載模型文件
```

## 使用方法

### 啟動MCP Server

```bash
omniparser-mcp-server
```

### 配置

創建 `config.json` 文件來配置服務器:

```json
{
    "omniparser": {
        "som_model_path": "weights/icon_detect/model.pt",
        "caption_model_name": "florence2", 
        "caption_model_path": "weights/icon_caption_florence",
        "box_threshold": 0.05
    },
    "automation": {
        "screenshot_delay": 0.1,
        "action_delay": 0.5
    }
}
```

## MCP工具

### 屏幕解析工具

- `parse_screen`: 解析當前屏幕或指定視窗
- `get_elements`: 獲取屏幕上的可交互元素列表
- `find_element`: 根據描述查找特定元素

### 操作工具

- `click_element`: 點擊指定元素
- `type_text`: 輸入文字
- `drag_element`: 拖拽元素
- `scroll`: 滾動頁面

### 視窗管理工具

- `list_windows`: 列出所有可用視窗
- `focus_window`: 聚焦到指定視窗
- `capture_window`: 截取指定視窗

## 開發

### 項目結構

```
omniparser-mcp/
├── src/
│   └── omniparser_mcp/
│       ├── __init__.py
│       ├── server.py          # MCP服務器主入口
│       ├── omniparser_client.py  # OmniParser模型客戶端
│       ├── automation/        # 自動化操作模組
│       ├── window_manager/    # 視窗管理模組
│       └── tools/            # MCP工具定義
├── tests/
├── config.json
├── pyproject.toml
└── README.md
```

### 運行測試

```bash
pytest tests/
```

## 許可證

MIT License

## 貢獻

歡迎提交Issue和Pull Request！
