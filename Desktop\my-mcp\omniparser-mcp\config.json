{"omniparser": {"som_model_path": "../OmniParser-master/weights/icon_detect/model.pt", "caption_model_name": "florence2", "caption_model_path": "../OmniParser-master/weights/icon_caption_florence", "box_threshold": 0.05}, "automation": {"screenshot_delay": 0.1, "action_delay": 0.5, "confidence_threshold": 0.8}, "window_manager": {"supported_browsers": ["chrome", "firefox", "edge", "safari"], "supported_games": ["steam", "epic", "origin"], "window_detection_timeout": 5.0}, "logging": {"level": "INFO", "file": "omniparser_mcp.log", "max_size_mb": 10, "backup_count": 3}, "server": {"host": "localhost", "port": 8000, "max_concurrent_requests": 10}}