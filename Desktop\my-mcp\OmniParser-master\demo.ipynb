{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["model to cuda\n"]}], "source": ["from util.utils import get_som_labeled_img, check_ocr_box, get_caption_model_processor, get_yolo_model\n", "import torch\n", "from ultralytics import YOLO\n", "from PIL import Image\n", "device = 'cuda'\n", "model_path='weights/icon_detect/model.pt'\n", "\n", "som_model = get_yolo_model(model_path)\n", "\n", "som_model.to(device)\n", "print('model to {}'.format(device))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/sandbox/aurora/miniconda/envs/omni/lib/python3.12/site-packages/timm/models/layers/__init__.py:48: FutureWarning: Importing from timm.models.layers is deprecated, please import via timm.layers\n", "  warnings.warn(f\"Importing from {__name__} is deprecated, please import via timm.layers\", FutureWarning)\n", "Florence2LanguageForConditionalGeneration has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.\n", "  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes\n", "  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).\n", "  - If you are not the owner of the model architecture class, please contact the model code owner to update it.\n"]}], "source": ["# two choices for caption model: fine-tuned blip2 or florence2\n", "import importlib\n", "# import util.utils\n", "# importlib.reload(utils)\n", "from util.utils import get_som_labeled_img, check_ocr_box, get_caption_model_processor, get_yolo_model\n", "caption_model_processor = get_caption_model_processor(model_name=\"florence2\", model_name_or_path=\"weights/icon_caption_florence\", device=device)\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(device(type='cuda', index=0), ultralytics.models.yolo.model.YOLO)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["som_model.device, type(som_model) "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["image size: (1919, 1079)\n", "\n", "0: 736x1280 115 icons, 6.1ms\n", "Speed: 3.7ms preprocess, 6.1ms inference, 0.8ms postprocess per image at shape (1, 3, 736, 1280)\n", "len(filtered_boxes): 128 41\n", "time to get parsed content: 0.2532336711883545\n"]}], "source": ["# reload utils\n", "import importlib\n", "import utils\n", "importlib.reload(utils)\n", "# from utils import get_som_labeled_img, check_ocr_box, get_caption_model_processor, get_yolo_model\n", "\n", "image_path = 'imgs/google_page.png'\n", "image_path = 'imgs/windows_home.png'\n", "# image_path = 'imgs/windows_multitab.png'\n", "# image_path = 'imgs/omni3.jpg'\n", "# image_path = 'imgs/ios.png'\n", "image_path = 'imgs/word.png'\n", "# image_path = 'imgs/excel2.png'\n", "\n", "image = Image.open(image_path)\n", "image_rgb = image.convert('RGB')\n", "print('image size:', image.size)\n", "\n", "box_overlay_ratio = max(image.size) / 3200\n", "draw_bbox_config = {\n", "    'text_scale': 0.8 * box_overlay_ratio,\n", "    'text_thickness': max(int(2 * box_overlay_ratio), 1),\n", "    'text_padding': max(int(3 * box_overlay_ratio), 1),\n", "    'thickness': max(int(3 * box_overlay_ratio), 1),\n", "}\n", "BOX_TRESHOLD = 0.05\n", "\n", "import time\n", "start = time.time()\n", "ocr_bbox_rslt, is_goal_filtered = check_ocr_box(image_path, display_img = False, output_bb_format='xyxy', goal_filtering=None, easyocr_args={'paragraph': False, 'text_threshold':0.9}, use_paddleocr=True)\n", "text, ocr_bbox = ocr_bbox_rslt\n", "cur_time_ocr = time.time() \n", "\n", "dino_labled_img, label_coordinates, parsed_content_list = get_som_labeled_img(image_path, som_model, BOX_TRESHOLD = BOX_TRESHOLD, output_coord_in_ratio=True, ocr_bbox=ocr_bbox,draw_bbox_config=draw_bbox_config, caption_model_processor=caption_model_processor, ocr_text=text,use_local_semantics=True, iou_threshold=0.7, scale_img=False, batch_size=128)\n", "cur_time_caption = time.time() \n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7fe9005d39b0>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot dino_labled_img it is in base64\n", "import base64\n", "import matplotlib.pyplot as plt\n", "import io\n", "plt.figure(figsize=(15,15))\n", "\n", "image = Image.open(io.BytesIO(base64.b64decode(dino_labled_img)))\n", "plt.axis('off')\n", "\n", "plt.imshow(image)\n", "# print(len(parsed_content_list))\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>bbox</th>\n", "      <th>interactivity</th>\n", "      <th>content</th>\n", "      <th>source</th>\n", "      <th>ID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>text</td>\n", "      <td>[0.1500781625509262, 0.011121409013867378, 0.3...</td>\n", "      <td>False</td>\n", "      <td>Document 10.docx  General*  Last Modified: Jus...</td>\n", "      <td>box_ocr_content_ocr</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>text</td>\n", "      <td>[0.034392911940813065, 0.04726598784327507, 0....</td>\n", "      <td>False</td>\n", "      <td>Home</td>\n", "      <td>box_ocr_content_ocr</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>text</td>\n", "      <td>[0.22094841301441193, 0.048192769289016724, 0....</td>\n", "      <td>False</td>\n", "      <td>Mailings</td>\n", "      <td>box_ocr_content_ocr</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>text</td>\n", "      <td>[0.2595101594924927, 0.05004633963108063, 0.28...</td>\n", "      <td>False</td>\n", "      <td>Review</td>\n", "      <td>box_ocr_content_ocr</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>text</td>\n", "      <td>[0.31474727392196655, 0.05004633963108063, 0.3...</td>\n", "      <td>False</td>\n", "      <td>Help</td>\n", "      <td>box_ocr_content_ocr</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>icon</td>\n", "      <td>[0.9390129446983337, 0.9358046650886536, 0.946...</td>\n", "      <td>True</td>\n", "      <td>Adding a new item or element.</td>\n", "      <td>box_yolo_content_yolo</td>\n", "      <td>123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>icon</td>\n", "      <td>[0.27768561244010925, 0.14850999414920807, 0.2...</td>\n", "      <td>True</td>\n", "      <td>Paragraph Options</td>\n", "      <td>box_yolo_content_yolo</td>\n", "      <td>124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>icon</td>\n", "      <td>[0.3195086717605591, 0.3229200839996338, 0.332...</td>\n", "      <td>True</td>\n", "      <td>a blank space.</td>\n", "      <td>box_yolo_content_yolo</td>\n", "      <td>125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>icon</td>\n", "      <td>[0.08737725764513016, 0.14849473536014557, 0.0...</td>\n", "      <td>True</td>\n", "      <td>Paragraph Options</td>\n", "      <td>box_yolo_content_yolo</td>\n", "      <td>126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>icon</td>\n", "      <td>[0.7414714694023132, 0.0008225861238315701, 0....</td>\n", "      <td>True</td>\n", "      <td>M0,0L9,0 4.5,5z</td>\n", "      <td>box_yolo_content_yolo</td>\n", "      <td>127</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>128 rows × 6 columns</p>\n", "</div>"], "text/plain": ["     type                                               bbox  interactivity  \\\n", "0    text  [0.1500781625509262, 0.011121409013867378, 0.3...          False   \n", "1    text  [0.034392911940813065, 0.04726598784327507, 0....          False   \n", "2    text  [0.22094841301441193, 0.048192769289016724, 0....          False   \n", "3    text  [0.2595101594924927, 0.05004633963108063, 0.28...          <PERSON><PERSON>e   \n", "4    text  [0.31474727392196655, 0.05004633963108063, 0.3...          <PERSON><PERSON>e   \n", "..    ...                                                ...            ...   \n", "123  icon  [0.9390129446983337, 0.9358046650886536, 0.946...           True   \n", "124  icon  [0.27768561244010925, 0.14850999414920807, 0.2...           True   \n", "125  icon  [0.3195086717605591, 0.3229200839996338, 0.332...           True   \n", "126  icon  [0.08737725764513016, 0.14849473536014557, 0.0...           True   \n", "127  icon  [0.7414714694023132, 0.0008225861238315701, 0....           True   \n", "\n", "                                               content                 source  \\\n", "0    Document 10.docx  General*  Last Modified: Jus...    box_ocr_content_ocr   \n", "1                                                 Home    box_ocr_content_ocr   \n", "2                                             Mailings    box_ocr_content_ocr   \n", "3                                               Review    box_ocr_content_ocr   \n", "4                                                 Help    box_ocr_content_ocr   \n", "..                                                 ...                    ...   \n", "123                      Adding a new item or element.  box_yolo_content_yolo   \n", "124                                  Paragraph Options  box_yolo_content_yolo   \n", "125                                     a blank space.  box_yolo_content_yolo   \n", "126                                  Paragraph Options  box_yolo_content_yolo   \n", "127                                    M0,0L9,0 4.5,5z  box_yolo_content_yolo   \n", "\n", "      ID  \n", "0      0  \n", "1      1  \n", "2      2  \n", "3      3  \n", "4      4  \n", "..   ...  \n", "123  123  \n", "124  124  \n", "125  125  \n", "126  126  \n", "127  127  \n", "\n", "[128 rows x 6 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df = pd.DataFrame(parsed_content_list)\n", "df['ID'] = range(len(df))\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'type': 'text',\n", "  'bbox': [0.1500781625509262,\n", "   0.011121409013867378,\n", "   0.3272537887096405,\n", "   0.03521779552102089],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Document 10.docx  General*  Last Modified: Just now ',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.034392911940813065,\n", "   0.04726598784327507,\n", "   0.05523710325360298,\n", "   0.07228915393352509],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Home',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.22094841301441193,\n", "   0.048192769289016724,\n", "   0.2542991042137146,\n", "   0.07321593910455704],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Mailings',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.2595101594924927,\n", "   0.05004633963108063,\n", "   0.2845231890678406,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Review',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.31474727392196655,\n", "   0.05004633963108063,\n", "   0.3335070312023163,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Help',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.06357477605342865,\n", "   0.05189990624785423,\n", "   0.08546117693185806,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Insert',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.08963001519441605,\n", "   0.05189990624785423,\n", "   0.11047420650720596,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Draw',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.11672746390104294,\n", "   0.05189990624785423,\n", "   0.14069828391075134,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Design',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.14799374341964722,\n", "   0.05189990624785423,\n", "   0.17300677299499512,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Layout',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.1792600303888321,\n", "   0.05189990624785423,\n", "   0.21677957475185394,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'References',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.29077643156051636,\n", "   0.05189990624785423,\n", "   0.3095362186431885,\n", "   0.07136237621307373],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'View',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.028660761192440987,\n", "   0.09638553857803345,\n", "   0.06253256648778915,\n", "   0.12789620459079742],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'B Copy',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.5054715871810913,\n", "   0.0991658940911293,\n", "   0.5622720122337341,\n", "   0.12882298231124878],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'No Spacing',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.7436164617538452,\n", "   0.1010194644331932,\n", "   0.7821782231330872,\n", "   0.12696941196918488],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': ' Replace',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.8936946392059326,\n", "   0.11028730124235153,\n", "   0.9405940771102905,\n", "   0.13067655265331268],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Editor <PERSON><PERSON><PERSON>',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.03230849280953407,\n", "   0.14735867083072662,\n", "   0.06253256648778915,\n", "   0.16682113707065582],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Clipboard',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.1771756112575531,\n", "   0.14550510048866272,\n", "   0.19593538343906403,\n", "   0.16682113707065582],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Font',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.34184470772743225,\n", "   0.14365153014659882,\n", "   0.3751954138278961,\n", "   0.16867469251155853],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': ' Paragraph',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.5747785568237305,\n", "   0.14550510048866272,\n", "   0.595622718334198,\n", "   0.16682113707065582],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Styles',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.3303804099559784,\n", "   0.3271547853946686,\n", "   0.5528921484947205,\n", "   0.35125115513801575],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Select the icon or press Alt + i to draft with Co<PERSON><PERSON>',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.002084418898448348,\n", "   0.9360519051551819,\n", "   0.20427305996418,\n", "   0.9592214822769165],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'Page 1 of1 Owords English (United States) Text Predictions: On',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.7519541382789612,\n", "   0.9341983199119568,\n", "   0.9051589369773865,\n", "   0.9592214822769165],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': 'DisplaySettings Focus ',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'text',\n", "  'bbox': [0.9676914811134338,\n", "   0.937905490398407,\n", "   0.9989578127861023,\n", "   0.9573679566383362],\n", "  'interactivity': <PERSON><PERSON><PERSON>,\n", "  'content': '+100%',\n", "  'source': 'box_ocr_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.4421736001968384,\n", "   0.08184637874364853,\n", "   0.503544270992279,\n", "   0.14312541484832764],\n", "  'interactivity': True,\n", "  'content': 'Normal ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.8202129602432251,\n", "   0.0799439325928688,\n", "   0.8537724018096924,\n", "   0.16541729867458344],\n", "  'interactivity': True,\n", "  'content': 'Sensitivity Sensitivity ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.2829877436161041,\n", "   0.9624102711677551,\n", "   0.3987997770309448,\n", "   0.993495523929596],\n", "  'interactivity': True,\n", "  'content': 'Q Search ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.09709537774324417,\n", "   0.07982321828603745,\n", "   0.1656084805727005,\n", "   0.10813647508621216],\n", "  'interactivity': True,\n", "  'content': 'A<PERSON><PERSON> (Body) ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.032580457627773285,\n", "   0.1255720555782318,\n", "   0.09002603590488434,\n", "   0.1463983952999115],\n", "  'interactivity': True,\n", "  'content': ' Format Painter ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.8902588486671448,\n", "   0.044809550046920776,\n", "   0.9379482865333557,\n", "   0.07267674803733826],\n", "  'interactivity': True,\n", "  'content': ' Editing  ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.8594683408737183,\n", "   0.07990165799856186,\n", "   0.8862223029136658,\n", "   0.16565215587615967],\n", "  'interactivity': True,\n", "  'content': 'Add-ins Add-ins ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.937203586101532,\n", "   0.043972600251436234,\n", "   0.9783244729042053,\n", "   0.07226086407899857],\n", "  'interactivity': True,\n", "  'content': ' Share ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.8398228883743286,\n", "   0.045797280967235565,\n", "   0.8904628157615662,\n", "   0.0720185711979866],\n", "  'interactivity': True,\n", "  'content': ' Comments ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.6806025505065918,\n", "   0.09105163812637329,\n", "   0.7179913520812988,\n", "   0.13343217968940735],\n", "  'interactivity': True,\n", "  'content': 'Title ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.03349877893924713,\n", "   0.07261770963668823,\n", "   0.09314046800136566,\n", "   0.10159756988286972],\n", "  'interactivity': True,\n", "  'content': 'X Cut ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.7914316058158875,\n", "   0.08070200681686401,\n", "   0.8148494362831116,\n", "   0.16481080651283264],\n", "  'interactivity': True,\n", "  'content': 'Dictate Voice ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.6167120337486267,\n", "   0.09614628553390503,\n", "   0.6672250032424927,\n", "   0.12657514214515686],\n", "  'interactivity': True,\n", "  'content': 'Heading 2 ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.009730310179293156,\n", "   0.07482017576694489,\n", "   0.033241190016269684,\n", "   0.14442680776119232],\n", "  'interactivity': True,\n", "  'content': 'Paste ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.5577442049980164,\n", "   0.0943688377737999,\n", "   0.6113258600234985,\n", "   0.13046984374523163],\n", "  'interactivity': True,\n", "  'content': 'Heading ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.8793162703514099,\n", "   0.9656901955604553,\n", "   0.8976719975471497,\n", "   0.9917699098587036],\n", "  'interactivity': True,\n", "  'content': 'ENG ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.7451575994491577,\n", "   0.14763490855693817,\n", "   0.785307765007019,\n", "   0.1652723103761673],\n", "  'interactivity': True,\n", "  'content': 'Editing ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.9438611268997192,\n", "   0.9580943584442139,\n", "   0.9950194954872131,\n", "   0.996694803237915],\n", "  'interactivity': True,\n", "  'content': '3:31 PM 11/11/2024 ',\n", "  'source': 'box_yolo_content_ocr'},\n", " {'type': 'icon',\n", "  'bbox': [0.7449547648429871,\n", "   0.12651577591896057,\n", "   0.7839282155036926,\n", "   0.1466185748577118],\n", "  'interactivity': True,\n", "  'content': 'Select TV',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7444393634796143,\n", "   0.07407090067863464,\n", "   0.7835544943809509,\n", "   0.09996039420366287],\n", "  'interactivity': True,\n", "  'content': 'Find and Find',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.1647457480430603,\n", "   0.08069710433483124,\n", "   0.18946485221385956,\n", "   0.10782845318317413],\n", "  'interactivity': True,\n", "  'content': 'a calendar date.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.8911049962043762,\n", "   0.0784681960940361,\n", "   0.9160676598548889,\n", "   0.13054102659225464],\n", "  'interactivity': True,\n", "  'content': 'Editor',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.032848671078681946,\n", "   0.10011783987283707,\n", "   0.09088698029518127,\n", "   0.12493246048688889],\n", "  'interactivity': True,\n", "  'content': 'Copy',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.21911193430423737,\n", "   0.08517386019229889,\n", "   0.23563091456890106,\n", "   0.10504699498414993],\n", "  'interactivity': True,\n", "  'content': '<PERSON><PERSON>',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.28900355100631714,\n", "   0.08490058034658432,\n", "   0.3082895278930664,\n", "   0.10591930896043777],\n", "  'interactivity': True,\n", "  'content': 'Bullets',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.28772780299186707,\n", "   0.1114562526345253,\n", "   0.30302634835243225,\n", "   0.13835911452770233],\n", "  'interactivity': True,\n", "  'content': 'Left',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.2691681981086731,\n", "   0.11370079219341278,\n", "   0.2802704870700836,\n", "   0.1362292319536209],\n", "  'interactivity': True,\n", "  'content': 'Xray',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.254993736743927,\n", "   0.11380063742399216,\n", "   0.266611248254776,\n", "   0.1355709433555603],\n", "  'interactivity': True,\n", "  'content': 'Text Box',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.24165970087051392,\n", "   0.08429771661758423,\n", "   0.25338807702064514,\n", "   0.10564447194337845],\n", "  'interactivity': True,\n", "  'content': 'Clear Formatting',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3584938645362854,\n", "   0.11388512700796127,\n", "   0.3771596848964691,\n", "   0.13681653141975403],\n", "  'interactivity': True,\n", "  'content': 'Increase',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.41244059801101685,\n", "   0.010012052953243256,\n", "   0.4257594347000122,\n", "   0.03476937860250473],\n", "  'interactivity': True,\n", "  'content': 'Find',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3167100250720978,\n", "   0.11221753060817719,\n", "   0.3286402225494385,\n", "   0.13693109154701233],\n", "  'interactivity': True,\n", "  'content': 'Center',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.5834749937057495,\n", "   0.01030009612441063,\n", "   0.5952306985855103,\n", "   0.03519187122583389],\n", "  'interactivity': True,\n", "  'content': 'Start Dictation',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9155872464179993,\n", "   0.0774986743927002,\n", "   0.9414986371994019,\n", "   0.13043203949928284],\n", "  'interactivity': True,\n", "  'content': 'Capilot',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.23385755717754364,\n", "   0.11361817270517349,\n", "   0.25300320982933044,\n", "   0.13530120253562927],\n", "  'interactivity': True,\n", "  'content': 'Clear Font Size',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.05260471627116203,\n", "   0.009093696251511574,\n", "   0.07912778109312057,\n", "   0.03518577665090561],\n", "  'interactivity': True,\n", "  'content': 'on',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.31092071533203125,\n", "   0.08435462415218353,\n", "   0.32932621240615845,\n", "   0.1058521643280983],\n", "  'interactivity': True,\n", "  'content': 'Numbering',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.19138741493225098,\n", "   0.1136801689863205,\n", "   0.2088528722524643,\n", "   0.13493822515010834],\n", "  'interactivity': True,\n", "  'content': 'Text formatting options for a text formatting option.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3849482238292694,\n", "   0.08577591180801392,\n", "   0.40266716480255127,\n", "   0.10767436772584915],\n", "  'interactivity': True,\n", "  'content': 'Font Color Yellow',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.33283182978630066,\n", "   0.08451216667890549,\n", "   0.35067814588546753,\n", "   0.10656823962926865],\n", "  'interactivity': True,\n", "  'content': 'Increase',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3431825041770935,\n", "   0.11326557397842407,\n", "   0.3555176854133606,\n", "   0.13643527030944824],\n", "  'interactivity': True,\n", "  'content': 'Slide',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.4234571158885956,\n", "   0.08657482266426086,\n", "   0.4342575669288635,\n", "   0.10628419369459152],\n", "  'interactivity': True,\n", "  'content': 'Formatting Marks',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.40803176164627075,\n", "   0.0857568308711052,\n", "   0.41973698139190674,\n", "   0.10661926865577698],\n", "  'interactivity': True,\n", "  'content': 'Alphabetical',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.33026888966560364,\n", "   0.11280493438243866,\n", "   0.34174031019210815,\n", "   0.1366947591304779],\n", "  'interactivity': True,\n", "  'content': 'Justified',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.1159251406788826,\n", "   0.005373313091695309,\n", "   0.14385069906711578,\n", "   0.036719486117362976],\n", "  'interactivity': True,\n", "  'content': 'Underline',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.14717529714107513,\n", "   0.11490598320960999,\n", "   0.1608850657939911,\n", "   0.13561619818210602],\n", "  'interactivity': True,\n", "  'content': 'Strikethrough',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3821178674697876,\n", "   0.11374185979366302,\n", "   0.4016681909561157,\n", "   0.13747844099998474],\n", "  'interactivity': True,\n", "  'content': 'Vibration',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.269164115190506,\n", "   0.08428816497325897,\n", "   0.28033247590065,\n", "   0.10548986494541168],\n", "  'interactivity': True,\n", "  'content': 'Text Box',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.20385253429412842,\n", "   0.08413325995206833,\n", "   0.21378879249095917,\n", "   0.10445793718099594],\n", "  'interactivity': True,\n", "  'content': '<PERSON><PERSON>',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.16311806440353394,\n", "   0.11534248292446136,\n", "   0.17403924465179443,\n", "   0.13565802574157715],\n", "  'interactivity': True,\n", "  'content': 'Subscript',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9052767157554626,\n", "   0.008148334920406342,\n", "   0.923717737197876,\n", "   0.03809784725308418],\n", "  'interactivity': True,\n", "  'content': 'Edit',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.40295663475990295,\n", "   0.11418107151985168,\n", "   0.42298439145088196,\n", "   0.1374422162771225],\n", "  'interactivity': True,\n", "  'content': 'Grid',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.30401983857154846,\n", "   0.11177166551351547,\n", "   0.31476956605911255,\n", "   0.13749627768993378],\n", "  'interactivity': True,\n", "  'content': 'Center',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7243527173995972,\n", "   0.11843118816614151,\n", "   0.7368354797363281,\n", "   0.14324237406253815],\n", "  'interactivity': True,\n", "  'content': 'Checklist',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.1005261242389679,\n", "   0.11491625010967255,\n", "   0.11139624565839767,\n", "   0.1337515264749527],\n", "  'interactivity': True,\n", "  'content': 'Bold',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7203767895698547,\n", "   0.9603809118270874,\n", "   0.7408540844917297,\n", "   0.9975048303604126],\n", "  'interactivity': True,\n", "  'content': 'Microsoft Word.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.17647777497768402,\n", "   0.11430791765451431,\n", "   0.18667808175086975,\n", "   0.13491767644882202],\n", "  'interactivity': True,\n", "  'content': 'Superscript',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.855280876159668,\n", "   0.008874936029314995,\n", "   0.8746954202651978,\n", "   0.037986889481544495],\n", "  'interactivity': True,\n", "  'content': 'Profile',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.2123347818851471,\n", "   0.1137116402387619,\n", "   0.2324122041463852,\n", "   0.13519783318042755],\n", "  'interactivity': True,\n", "  'content': 'Yellow',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.40128636360168457,\n", "   0.9643728733062744,\n", "   0.418992280960083,\n", "   0.9973303079605103],\n", "  'interactivity': True,\n", "  'content': 'Microsoft Edge browser',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.09717030078172684,\n", "   0.006188888102769852,\n", "   0.1149354875087738,\n", "   0.03614520654082298],\n", "  'interactivity': True,\n", "  'content': 'Undo',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.2558187246322632,\n", "   0.08481919020414352,\n", "   0.26677441596984863,\n", "   0.10527196526527405],\n", "  'interactivity': True,\n", "  'content': 'Spelling',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.2608030140399933,\n", "   0.9643219709396362,\n", "   0.2798607647418976,\n", "   0.9920912384986877],\n", "  'interactivity': True,\n", "  'content': 'Windows.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9542849063873291,\n", "   0.007898959331214428,\n", "   0.9719269275665283,\n", "   0.036600250750780106],\n", "  'interactivity': True,\n", "  'content': 'Delete',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9295913577079773,\n", "   0.008353901095688343,\n", "   0.9459232687950134,\n", "   0.03581923618912697],\n", "  'interactivity': True,\n", "  'content': 'Minimize',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7249855995178223,\n", "   0.09943164885044098,\n", "   0.7370039820671082,\n", "   0.12186257541179657],\n", "  'interactivity': True,\n", "  'content': 'Expand to the previous item',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.6986755132675171,\n", "   0.9619225263595581,\n", "   0.7189759016036987,\n", "   0.9966652989387512],\n", "  'interactivity': True,\n", "  'content': 'Teams 1',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.468858927488327,\n", "   0.9603828191757202,\n", "   0.4896562695503235,\n", "   0.9965057969093323],\n", "  'interactivity': True,\n", "  'content': 'Outlook',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.6761324405670166,\n", "   0.9637606143951416,\n", "   0.6941070556640625,\n", "   0.9978272914886475],\n", "  'interactivity': True,\n", "  'content': 'Microsoft Edge browser',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.4936092495918274,\n", "   0.9623907208442688,\n", "   0.5103288292884827,\n", "   0.9960803985595703],\n", "  'interactivity': True,\n", "  'content': 'Microsoft Edge browser',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9793755412101746,\n", "   0.008350052870810032,\n", "   0.9978001117706299,\n", "   0.037221796810626984],\n", "  'interactivity': True,\n", "  'content': 'Close',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.6313118934631348,\n", "   0.9642506241798401,\n", "   0.6476805806159973,\n", "   0.9950335621833801],\n", "  'interactivity': True,\n", "  'content': 'Microsoft 365',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.882165789604187,\n", "   0.007914714515209198,\n", "   0.8968138098716736,\n", "   0.03773743659257889],\n", "  'interactivity': True,\n", "  'content': 'Help',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.29840603470802307,\n", "   0.3183053731918335,\n", "   0.3177575170993805,\n", "   0.3532159924507141],\n", "  'interactivity': True,\n", "  'content': 'Copy',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3559046685695648,\n", "   0.08475767821073532,\n", "   0.36883625388145447,\n", "   0.10659159719944],\n", "  'interactivity': True,\n", "  'content': 'Decrease',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.6540431380271912,\n", "   0.9651435613632202,\n", "   0.6705886721611023,\n", "   0.9966893792152405],\n", "  'interactivity': True,\n", "  'content': 'PowerPoint',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.11363180726766586,\n", "   0.11444833874702454,\n", "   0.12478602677583694,\n", "   0.13346010446548462],\n", "  'interactivity': True,\n", "  'content': 'Italic',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.6090320348739624,\n", "   0.9621817469596863,\n", "   0.6264832615852356,\n", "   0.9961049556732178],\n", "  'interactivity': True,\n", "  'content': 'Keebok - Learning Center',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.537849485874176,\n", "   0.9638864398002625,\n", "   0.5561392307281494,\n", "   0.9978503584861755],\n", "  'interactivity': True,\n", "  'content': 'Movies & TV',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.4490187168121338,\n", "   0.962541401386261,\n", "   0.4631076753139496,\n", "   0.9957088828086853],\n", "  'interactivity': True,\n", "  'content': 'OneNote',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.003767237300053239,\n", "   0.005633704364299774,\n", "   0.021132590249180794,\n", "   0.03855128586292267],\n", "  'interactivity': True,\n", "  'content': 'Word',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9811702966690063,\n", "   0.043235111981630325,\n", "   0.9961761832237244,\n", "   0.07214152812957764],\n", "  'interactivity': True,\n", "  'content': 'Feedback',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.5169134736061096,\n", "   0.9640648365020752,\n", "   0.5334565043449402,\n", "   0.9957965612411499],\n", "  'interactivity': True,\n", "  'content': 'Microsoft Excel',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.42432302236557007,\n", "   0.9645397663116455,\n", "   0.441425621509552,\n", "   0.9964669942855835],\n", "  'interactivity': True,\n", "  'content': 'folder for Photos',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.1906655877828598,\n", "   0.08328729122877121,\n", "   0.20065614581108093,\n", "   0.10488089919090271],\n", "  'interactivity': True,\n", "  'content': 'Superscript',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.12777651846408844,\n", "   0.11477608978748322,\n", "   0.14602993428707123,\n", "   0.1350645124912262],\n", "  'interactivity': True,\n", "  'content': 'Underline',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.080568328499794,\n", "   0.007114470470696688,\n", "   0.09537970274686813,\n", "   0.03580892086029053],\n", "  'interactivity': True,\n", "  'content': 'Properties',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.8606500625610352,\n", "   0.9657168984413147,\n", "   0.8758905529975891,\n", "   0.9911593198776245],\n", "  'interactivity': True,\n", "  'content': 'Weather forecast',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.5619350671768188,\n", "   0.9625930786132812,\n", "   0.5788809061050415,\n", "   0.9987186789512634],\n", "  'interactivity': True,\n", "  'content': 'Toggle Terminal',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.5840401649475098,\n", "   0.9613277912139893,\n", "   0.6028914451599121,\n", "   0.997545599937439],\n", "  'interactivity': True,\n", "  'content': 'Microsoft Edge browser',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.828442394733429,\n", "   0.9678291082382202,\n", "   0.8419186472892761,\n", "   0.9910253882408142],\n", "  'interactivity': True,\n", "  'content': 'M0,0L9,0 4.5,5z',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9818354249000549,\n", "   0.1431248039007187,\n", "   0.9959391355514526,\n", "   0.16661445796489716],\n", "  'interactivity': True,\n", "  'content': 'Ribbon display options',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7249932885169983,\n", "   0.08323707431554794,\n", "   0.736742377281189,\n", "   0.10332650691270828],\n", "  'interactivity': True,\n", "  'content': 'Movies or maps.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7324291467666626,\n", "   0.14760799705982208,\n", "   0.7400776147842407,\n", "   0.1655750423669815],\n", "  'interactivity': True,\n", "  'content': 'Paragraph Options',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.844497561454773,\n", "   0.9674856066703796,\n", "   0.8574309349060059,\n", "   0.9902262091636658],\n", "  'interactivity': True,\n", "  'content': 'Toggle Button',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.36990800499916077,\n", "   0.08577347546815872,\n", "   0.3810402452945709,\n", "   0.10632908344268799],\n", "  'interactivity': True,\n", "  'content': 'Increase',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9397377371788025,\n", "   0.07160456478595734,\n", "   0.9963635206222534,\n", "   0.1394500434398651],\n", "  'interactivity': True,\n", "  'content': 'a blank space.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9074587225914001,\n", "   0.9333956837654114,\n", "   0.9175535440444946,\n", "   0.9555543065071106],\n", "  'interactivity': True,\n", "  'content': 'Minimize',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9032755494117737,\n", "   0.96732497215271,\n", "   0.9353903532028198,\n", "   0.990323543548584],\n", "  'interactivity': True,\n", "  'content': 'Controls',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.43108275532722473,\n", "   0.14830924570560455,\n", "   0.4395733177661896,\n", "   0.16404278576374054],\n", "  'interactivity': True,\n", "  'content': 'Paragraph Options',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.9390129446983337,\n", "   0.9358046650886536,\n", "   0.9462238550186157,\n", "   0.9563426375389099],\n", "  'interactivity': True,\n", "  'content': 'Adding a new item or element.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.27768561244010925,\n", "   0.14850999414920807,\n", "   0.2840486466884613,\n", "   0.1640910804271698],\n", "  'interactivity': True,\n", "  'content': 'Paragraph Options',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.3195086717605591,\n", "   0.3229200839996338,\n", "   0.3328612446784973,\n", "   0.3530133366584778],\n", "  'interactivity': True,\n", "  'content': 'a blank space.',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.08737725764513016,\n", "   0.14849473536014557,\n", "   0.09547986835241318,\n", "   0.16415591537952423],\n", "  'interactivity': True,\n", "  'content': 'Paragraph Options',\n", "  'source': 'box_yolo_content_yolo'},\n", " {'type': 'icon',\n", "  'bbox': [0.7414714694023132,\n", "   0.0008225861238315701,\n", "   0.783491849899292,\n", "   0.05007069185376167],\n", "  'interactivity': True,\n", "  'content': 'M0,0L9,0 4.5,5z',\n", "  'source': 'box_yolo_content_yolo'}]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["parsed_content_list"]}], "metadata": {"kernelspec": {"display_name": "omni", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 2}