{"_comment": "MCP配置示例 - 選擇適合您環境的配置方式", "option1_uv_tool_run": {"_description": "使用uv tool run（推薦）", "mcpServers": {"omniparser": {"command": "uv", "args": ["tool", "run", "omniparser-mcp-server"]}}}, "option2_uv_tool_with_config": {"_description": "使用uv tool run並指定配置文件", "mcpServers": {"omniparser": {"command": "uv", "args": ["tool", "run", "omniparser-mcp-server", "--config", "/path/to/your/config.json"]}}}, "option3_pip_installed": {"_description": "如果已通過pip安裝", "mcpServers": {"omniparser": {"command": "omniparser-mcp-server", "args": []}}}, "option4_python_module": {"_description": "直接運行Python模組（開發模式）", "mcpServers": {"omniparser": {"command": "python", "args": ["-m", "omniparser_mcp.server"], "cwd": "/path/to/omniparser-mcp-server"}}}, "option5_with_environment": {"_description": "包含環境變量的配置", "mcpServers": {"omniparser": {"command": "uv", "args": ["tool", "run", "omniparser-mcp-server"], "env": {"OMNIPARSER_LOG_LEVEL": "DEBUG", "OMNIPARSER_CONFIG_PATH": "/custom/path/to/config.json"}}}}, "installation_instructions": {"_comment": "安裝說明", "step1": "確保已安裝uv: curl -LsSf https://astral.sh/uv/install.sh | sh", "step2": "安裝MCP服務器: uv tool install omniparser-mcp-server", "step3": "或者從源碼安裝: uv tool install git+https://github.com/yourusername/omniparser-mcp-server.git", "step4": "將上述配置之一添加到Claude Desktop配置文件中"}, "claude_desktop_config_locations": {"macOS": "~/Library/Application Support/Claude/claude_desktop_config.json", "Windows": "%APPDATA%\\Claude\\claude_desktop_config.json"}}